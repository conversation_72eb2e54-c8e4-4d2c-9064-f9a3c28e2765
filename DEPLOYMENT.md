# Deployment Guide for Bloopix Website

## 🚀 Quick Deployment to Namecheap

### Step 1: Build the Website
```bash
npm run build
```

This creates a static export in the `out` folder.

### Step 2: Prepare Files for Upload
1. Navigate to the `out` folder
2. Select all files and folders inside `out`
3. Create a ZIP file containing all the contents

### Step 3: Upload to Namecheap
1. Log into your Namecheap hosting account
2. Go to cPanel or File Manager
3. Navigate to `public_html` directory
4. Upload the ZIP file
5. Extract the ZIP file in `public_html`
6. Delete the ZIP file after extraction

### Step 4: Verify Deployment
- Visit your domain to see the live website
- Test all links and functionality
- Check mobile responsiveness

## 📁 Files Structure After Deployment

Your `public_html` should contain:
```
public_html/
├── _next/
├── 404.html
├── index.html
├── bloopix-logo.jpeg
├── favicon.ico
└── other static assets...
```

## 🔧 Alternative Deployment Methods

### Netlify (Drag & Drop)
1. Run `npm run build`
2. Go to [netlify.com](https://netlify.com)
3. Drag the `out` folder to the deploy area
4. Your site is live instantly!

### Vercel (GitHub Integration)
1. Push your code to GitHub
2. Connect your GitHub repo to Vercel
3. Vercel automatically builds and deploys
4. Get a custom domain or use the provided URL

### GitHub Pages
1. Run `npm run build`
2. Push the `out` folder contents to a `gh-pages` branch
3. Enable GitHub Pages in repository settings
4. Your site is live at `username.github.io/repo-name`

## 🌐 Custom Domain Setup

### For Namecheap:
1. Point your domain to your hosting
2. Ensure files are in `public_html`
3. Wait for DNS propagation (up to 24 hours)

### For Other Providers:
1. Update DNS settings to point to your hosting provider
2. Follow provider-specific instructions
3. Enable SSL certificate for HTTPS

## 🔍 Troubleshooting

### Common Issues:

**404 Errors:**
- Ensure `index.html` is in the root directory
- Check file permissions (755 for folders, 644 for files)

**Images Not Loading:**
- Verify image files are uploaded
- Check file paths are correct
- Ensure proper file permissions

**CSS/JS Not Loading:**
- Verify `_next` folder is uploaded completely
- Check for any missing static assets
- Clear browser cache

**Mobile Issues:**
- Test on actual devices
- Use browser dev tools for mobile simulation
- Check viewport meta tag is present

## 📊 Performance Optimization

### After Deployment:
1. **Enable Gzip Compression** (usually available in cPanel)
2. **Set up CDN** (Cloudflare is free and works well)
3. **Enable Browser Caching** (add .htaccess rules)
4. **Monitor Performance** (use Google PageSpeed Insights)

### Sample .htaccess for Performance:
```apache
# Enable Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

## 🔒 Security Considerations

1. **HTTPS**: Always use SSL certificates
2. **File Permissions**: Set correct permissions (755/644)
3. **Regular Updates**: Keep hosting environment updated
4. **Backup**: Regular backups of your website files

## 📞 Support

If you encounter issues:
1. Check Namecheap documentation
2. Contact Namecheap support
3. Review this deployment guide
4. Check the main README.md for technical details

---

🌊 Happy deploying! Let's get Bloopix live and making waves!
