# Bloopix ($BLP) - Official Website

A professional, responsive website for the Bloopix memecoin built with Next.js and Tailwind CSS.

## 🌊 About Bloopix

From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token designed for degens who don&apos;t take themselves too seriously but know how to make waves.

## 🚀 Features

- **Modern Design**: Professional, responsive design with smooth animations
- **Mobile-First**: Fully responsive across all devices
- **Fast Loading**: Optimized for performance with Next.js
- **SEO Optimized**: Complete meta tags and Open Graph support
- **Static Export**: Ready for deployment to any static hosting service

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS
- **Language**: TypeScript
- **Deployment**: Static export for easy hosting

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd bloopix-website
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Build & Deploy

### For Namecheap or any Static Hosting

1. Build the static export:
```bash
npm run build
```

2. The static files will be generated in the `out` folder.

3. Upload the contents of the `out` folder to your hosting provider:
   - **For Namecheap**: Upload to your domain&apos;s `public_html` folder
   - **For Netlify**: Drag and drop the `out` folder
   - **For Vercel**: Connect your GitHub repository

### Deployment Commands

```bash
# Development
npm run dev

# Build for production
npm run build

# Start production server (if needed)
npm start

# Lint code
npm run lint
```

## 📁 Project Structure

```
bloopix-website/
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── components/
│       ├── Hero.tsx
│       ├── About.tsx
│       ├── Tokenomics.tsx
│       ├── Roadmap.tsx
│       └── Footer.tsx
├── public/
│   └── bloopix-logo.jpeg
├── out/ (generated after build)
├── next.config.js
└── package.json
```

## 🎨 Components

- **Hero**: Landing section with logo, description, and CTA buttons
- **About**: Vision, mission, and key features
- **Tokenomics**: Token distribution and bonding curve explanation
- **Roadmap**: Development phases and community growth
- **Footer**: Social links, contract address, and additional info

## 🔗 Important Links

- **Buy $BLP**: https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG
- **X (Twitter)**: @BLOOPIX11
- **Contract**: 9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG

## 🎯 SEO & Performance

- Optimized meta tags for social sharing
- Open Graph and Twitter Card support
- Fast loading with Next.js optimization
- Mobile-responsive design
- Accessibility features

## 📱 Responsive Design

The website is fully responsive and tested on:
- Desktop (1920px+)
- Laptop (1024px - 1919px)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🎨 Customization

To customize the website:

1. **Colors**: Edit the Tailwind classes in components
2. **Content**: Update text in component files
3. **Images**: Replace logo in `public/` folder
4. **Links**: Update social and buy links in components

## 🚀 Quick Deployment to Namecheap

1. Run `npm run build`
2. Zip the contents of the `out` folder
3. Upload to your Namecheap hosting via File Manager
4. Extract in the `public_html` directory
5. Your site is live!

## ⚠️ Disclaimer

Bloopix is a meme token launched purely for fun and community. It has no intrinsic value, no promised utility, and no guarantee of profit.

---

Built with 💙 by the Bloopix community. Let&apos;s make some waves! 🌊
