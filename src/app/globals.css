@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  width: 100%;
  overflow-x: hidden;
}

body {
  font-family: 'Poppins', 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  width: 100%;
  overflow-x: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Ensure all containers are properly centered */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Professional section spacing with moderate breathing room */
section {
  width: 100%;
  position: relative;
  padding: 4rem 0; /* Moderate vertical spacing */
  margin: 0;
  overflow: hidden; /* Prevent content overflow */
}

/* Hero section gets special treatment */
section:first-child {
  padding: 0;
  min-height: 100vh;
}

/* Force all content to be centered with proper spacing */
section > div {
  width: 100%;
  max-width: 1400px; /* Increased max-width for better use of space */
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0 2rem; /* Horizontal padding for mobile */
}

/* Professional section separators with better visual hierarchy */
section:not(:first-child):not(:last-child) {
  position: relative;
}

section:not(:first-child):not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(147, 51, 234, 0.2), transparent);
}

/* Override any left-floating elements */
* {
  box-sizing: border-box;
}

.force-center {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  width: 100% !important;
  margin: 0 auto !important;
}

/* Professional spacing utilities */
.section-spacing {
  padding: 8rem 0;
}

.content-spacing {
  margin-bottom: 4rem;
}

.large-spacing {
  margin-bottom: 6rem;
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
  section {
    padding: 4rem 0;
  }

  section > div {
    padding: 0 1rem;
  }

  .section-spacing {
    padding: 5rem 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #2563eb, #7c3aed);
}

/* Professional card styling with consistent heights */
.professional-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 280px;
}

/* FAQ card specific styling */
.faq-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: auto;
}

.professional-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(147, 51, 234, 0.3);
}

/* Grid layouts with proper spacing */
.card-grid {
  display: grid;
  gap: 2rem;
  align-items: stretch;
}

@media (min-width: 640px) {
  .card-grid {
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .card-grid {
    gap: 4rem;
  }
}

/* Ensure proper spacing between sections */
.section-spacing {
  margin-bottom: 6rem;
}

@media (min-width: 768px) {
  .section-spacing {
    margin-bottom: 8rem;
  }
}

/* Prevent card overlapping */
.grid > * {
  position: relative;
  z-index: 1;
}

/* Ensure proper spacing for mobile */
@media (max-width: 640px) {
  .professional-card {
    margin-bottom: 3rem;
    min-height: 280px;
  }

  .grid {
    gap: 2rem !important;
  }
}

/* Ensure proper spacing for tablets */
@media (min-width: 641px) and (max-width: 1023px) {
  .professional-card {
    margin-bottom: 2rem;
    min-height: 300px;
  }

  .grid {
    gap: 3rem !important;
  }
}

/* Ensure proper spacing for desktop */
@media (min-width: 1024px) {
  .professional-card {
    margin-bottom: 1rem;
    min-height: 320px;
  }

  .grid {
    gap: 4rem !important;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bubble {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) scale(1);
    opacity: 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-bubble {
  animation: bubble 8s linear infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Professional animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
  }
}

/* Professional utility classes */
.animate-slide-up {
  animation: slideInUp 0.8s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Professional card styles */
.professional-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.professional-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

/* Professional button styles */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: white;
  color: #667eea;
  transform: translateY(-3px);
}

/* Blob animation for background elements */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Enhanced border styles */
.border-3 {
  border-width: 3px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .professional-card {
    padding: 1.5rem;
  }

  .animate-float {
    animation-duration: 4s;
  }

  /* Reduce motion for mobile performance */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-pulse,
    .animate-bounce,
    .animate-blob {
      animation: none;
    }
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .professional-card:hover {
    transform: none;
  }

  .btn-primary:hover,
  .btn-secondary:hover {
    transform: none;
  }
}

/* Ensure proper text rendering on mobile */
@media (max-width: 640px) {
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}
