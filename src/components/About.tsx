'use client';

import { useState, useEffect, useRef } from 'react';

export default function About() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const features = [
    {
      icon: '🌊',
      title: 'Community-Driven',
      description: 'Built by degens, for degens. No VCs, no team tokens, just pure community vibes.'
    },
    {
      icon: '🎯',
      title: 'Fair Launch',
      description: '100% fair launch on Pumpfun. Everyone starts at the same price, no pre-sales or allocations.'
    },
    {
      icon: '🚀',
      title: 'Meme Power',
      description: 'Powered by memes, viral content, and the collective energy of the Bloopix community.'
    },
    {
      icon: '💎',
      title: 'Diamond Hands',
      description: 'For holders who believe in the power of fun, community, and making waves in crypto.'
    }
  ];

  return (
    <section id="about" ref={sectionRef} className="w-full bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Professional Section Header */}
          <div className="text-center mb-20">
            <div className="inline-block mb-8">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent text-lg font-bold uppercase tracking-wider">
                About Us
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-12 leading-tight">
              About{' '}
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent">
                Bloopix
              </span>
            </h2>
            <div className="w-32 h-1.5 bg-gradient-to-r from-purple-600 to-pink-600 mx-auto mb-12 rounded-full"></div>
            <p className="text-xl sm:text-2xl text-gray-700 max-w-5xl mx-auto leading-relaxed font-medium">
              Bloopix is here to remind everyone that crypto doesn&apos;t always have to be serious.
              Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto
              the scene to deliver joy and a light-hearted reminder of what brought many of us here.
            </p>
          </div>

          {/* Professional Vision & Mission - CENTERED */}
          <div className="w-full flex flex-col items-center justify-center mb-32">
            <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-12 lg:gap-16">
              <div className="professional-card p-8 lg:p-10 group hover:scale-105 transition-all duration-500 text-center h-full flex flex-col">
                <div className="flex flex-col items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center text-3xl mb-4 group-hover:rotate-12 transition-transform duration-300">
                    🎯
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-black text-gray-900">Our Vision</h3>
                </div>
                <p className="text-gray-700 text-base lg:text-lg leading-relaxed text-center flex-grow">
                  Bloopix is here to remind everyone that crypto doesn&apos;t always have to be serious.
                  Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto
                  the scene to deliver joy and a light-hearted reminder of what brought many of us
                  here in the first place: curiosity, community, and a little chaos.
                </p>
                <div className="mt-6 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              </div>

              <div className="professional-card p-8 lg:p-10 group hover:scale-105 transition-all duration-500 text-center h-full flex flex-col">
                <div className="flex flex-col items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-3xl mb-4 group-hover:rotate-12 transition-transform duration-300">
                    🚀
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-black text-gray-900">Our Mission</h3>
                </div>
                <ul className="text-gray-700 text-sm lg:text-base space-y-3 lg:space-y-4 text-left max-w-md mx-auto flex-grow">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                    <span>Inject humor and positivity into the Pumpfun scene</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                    <span>Build a vibrant, self-aware community of degens and dreamers</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                    <span>Ride the waves of Pumpfun while creating memorable experiences</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                    <span>Show that fun coins can still make big splashes</span>
                  </li>
                </ul>
                <div className="mt-6 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              </div>
            </div>
          </div>

          {/* Professional Features Grid - CENTERED */}
          <div className="w-full flex flex-col items-center justify-center mb-32">
            <div className="w-full max-w-7xl grid sm:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`professional-card p-8 lg:p-10 text-center group hover:scale-105 h-full flex flex-col ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                  }`}
                  style={{ transitionDelay: `${index * 150}ms` }}
                >
                  <div className="text-4xl lg:text-5xl mb-6 group-hover:scale-125 transition-transform duration-300">
                    {feature.icon}
                  </div>
                  <h4 className="text-lg lg:text-xl font-black text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                    {feature.title}
                  </h4>
                  <p className="text-gray-600 leading-relaxed text-sm lg:text-base flex-grow">
                    {feature.description}
                  </p>
                  <div className="mt-6 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Professional Why Bloopix Section */}
          <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 rounded-3xl p-12 md:p-16 text-white overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.4%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
            </div>

            <div className="relative z-10">
              <div className="text-center mb-12">
                <h3 className="text-4xl sm:text-5xl md:text-6xl font-black mb-6">
                  Why Bloopix?
                  <span className="inline-block ml-4 text-5xl animate-bounce">🤔</span>
                </h3>
                <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-pink-400 mx-auto rounded-full"></div>
              </div>

              <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-12 lg:gap-16">
                <div className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-2xl flex items-center justify-center text-4xl mb-6 mx-auto group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                    😊
                  </div>
                  <h4 className="text-xl font-bold mb-4 group-hover:text-yellow-300 transition-colors duration-300">
                    Cute & Relatable
                  </h4>
                  <p className="text-blue-100 leading-relaxed">
                    A happy, bubbly character designed to resonate with everyone
                  </p>
                </div>

                <div className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-400 rounded-2xl flex items-center justify-center text-4xl mb-6 mx-auto group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                    🧠
                  </div>
                  <h4 className="text-xl font-bold mb-4 group-hover:text-blue-300 transition-colors duration-300">
                    Memorable Branding
                  </h4>
                  <p className="text-blue-100 leading-relaxed">
                    Easy to recognize and share across all platforms
                  </p>
                </div>

                <div className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-blue-400 rounded-2xl flex items-center justify-center text-4xl mb-6 mx-auto group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                    👥
                  </div>
                  <h4 className="text-xl font-bold mb-4 group-hover:text-green-300 transition-colors duration-300">
                    Community-Oriented
                  </h4>
                  <p className="text-blue-100 leading-relaxed">
                    What matters is the people who hold it, share it, and spread the joy
                  </p>
                </div>

                <div className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center text-4xl mb-6 mx-auto group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                    😌
                  </div>
                  <h4 className="text-xl font-bold mb-4 group-hover:text-purple-300 transition-colors duration-300">
                    No Pressure
                  </h4>
                  <p className="text-blue-100 leading-relaxed">
                    No overblown roadmap or unrealistic promises - just good vibes
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
