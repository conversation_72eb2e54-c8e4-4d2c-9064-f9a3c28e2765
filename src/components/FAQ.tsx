'use client';

import { useState, useEffect, useRef } from 'react';

export default function FAQ() {
  const [isVisible, setIsVisible] = useState(false);
  const [openIndex, setOpenIndex] = useState<number | null>(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const faqs = [
    {
      question: "What is Bloopix ($BLP)?",
      answer: "Bloopix is a fun, community-driven memecoin launched on Pumpfun. It's designed to bring joy and humor to the crypto space while building a vibrant community of degens and dreamers who don't take themselves too seriously but know how to make waves."
    },
    {
      question: "How can I buy $BLP tokens?",
      answer: "You can buy $BLP tokens directly on Pumpfun using the link provided on our website. Simply connect your wallet, swap SOL for $BLP, and join our bubbly community!"
    },
    {
      question: "Is there a team allocation or pre-sale?",
      answer: "No! Bloopix follows a 100% fair launch model. There are no team tokens, no pre-sales, and no VC backing. Everyone starts at the same price on the bonding curve, making it completely fair for all participants."
    },
    {
      question: "What makes Bloopix different from other memecoins?",
      answer: "Bloopix focuses on genuine community building and fun rather than unrealistic promises. We're transparent about being a memecoin, embrace the chaos of crypto, and aim to create memorable experiences while riding the waves of market sentiment."
    },
    {
      question: "What is the tokenomics structure?",
      answer: "Bloopix follows Pumpfun's dynamic bonding curve system. The supply is dynamic based on demand, pricing follows the bonding curve (early buyers benefit), and there's 100% fair distribution with no team allocations."
    },
    {
      question: "Is there a roadmap or utility planned?",
      answer: "We intentionally avoid overblown roadmaps or unrealistic utility promises. Bloopix is about community, fun, and good vibes. Our 'roadmap' is simple: build an awesome community, create memorable experiences, and make waves in the crypto space."
    },
    {
      question: "How can I join the community?",
      answer: "Follow us on X (Twitter) @BLOOPIX11 for updates, memes, and community interactions. We're building a self-aware community of crypto enthusiasts who appreciate humor and don't take themselves too seriously."
    },
    {
      question: "Is Bloopix safe to invest in?",
      answer: "Like all memecoins and crypto investments, Bloopix carries risks. We're transparent about being a fun, community-driven project without utility promises. Only invest what you can afford to lose and always do your own research."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" ref={sectionRef} className="w-full bg-gradient-to-br from-purple-50 via-white to-pink-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Professional Section Header */}
          <div className="text-center mb-20">
            <div className="inline-block mb-8">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent text-lg font-bold uppercase tracking-wider">
                Got Questions?
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-12 leading-tight">
              Frequently Asked{' '}
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent">
                Questions
              </span>
            </h2>
            <div className="w-32 h-1.5 bg-gradient-to-r from-purple-600 to-pink-600 mx-auto mb-12 rounded-full"></div>
            <p className="text-xl sm:text-2xl text-gray-700 max-w-5xl mx-auto leading-relaxed font-medium">
              Everything you need to know about Bloopix and our bubbly community
            </p>
          </div>

          {/* FAQ Items */}
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className={`faq-card overflow-hidden transition-all duration-500 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-300"
                >
                  <h3 className="text-xl font-bold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <div className={`flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold transition-transform duration-300 ${
                    openIndex === index ? 'rotate-45' : ''
                  }`}>
                    +
                  </div>
                </button>
                
                <div className={`overflow-hidden transition-all duration-500 ${
                  openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="px-8 pb-6">
                    <div className="w-full h-px bg-gradient-to-r from-purple-200 to-pink-200 mb-4"></div>
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 text-white">
              <h3 className="text-3xl font-bold mb-4">Still have questions?</h3>
              <p className="text-xl mb-6 text-purple-100">
                Join our community and ask away! We're always happy to help.
              </p>
              <a
                href="https://x.com/bloopix11/status/1947960764603785465?s=46"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                🐦 Ask on X
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
