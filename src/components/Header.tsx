'use client';

import { useState } from 'react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50" style={{
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
      borderBottom: '1px solid rgba(255,255,255,0.1)'
    }}>
      <div className="max-w-7xl mx-auto px-8">
        <div className="flex items-center justify-between h-20">

          {/* Logo - Updated to match "MUCH WOW" design */}
          <div className="flex items-center space-x-3 cursor-pointer" onClick={() => scrollToSection('hero')}>
            <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-black font-bold text-lg">😊</span>
            </div>
            <span className="text-white font-black text-2xl tracking-wide">MUCH WOW</span>
          </div>

          {/* Center Navigation */}
          <nav className="hidden lg:flex items-center space-x-12">
            <button
              onClick={() => scrollToSection('about')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              Our Story
            </button>
            <button
              onClick={() => scrollToSection('tokenomics')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              Tokenomics
            </button>
            <button
              onClick={() => scrollToSection('roadmap')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              Roadmap
            </button>
            <button
              onClick={() => scrollToSection('faq')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              FAQ
            </button>
          </nav>

          {/* Right Side - Join us! button */}
          <div className="hidden lg:flex items-center">
            <button
              onClick={() => scrollToSection('hero')}
              className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-6 py-2 rounded-full text-base font-bold hover:from-yellow-300 hover:to-orange-400 transition-all duration-200 shadow-lg transform hover:scale-105"
            >
              Join us!
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden text-white p-2"
          >
            <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-gray-900/95 backdrop-blur-sm rounded-xl mt-4 py-6 mx-4 shadow-2xl border border-white/10">
            <div className="flex flex-col space-y-4 px-6">
              <button onClick={() => scrollToSection('about')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                Our Story
              </button>
              <button onClick={() => scrollToSection('tokenomics')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                Tokenomics
              </button>
              <button onClick={() => scrollToSection('roadmap')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                Roadmap
              </button>
              <button onClick={() => scrollToSection('faq')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                FAQ
              </button>

              <div className="flex flex-col space-y-4 pt-6 border-t border-white/20">
                <button
                  onClick={() => scrollToSection('hero')}
                  className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-8 py-4 rounded-full text-lg font-black shadow-xl"
                >
                  Join us!
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
