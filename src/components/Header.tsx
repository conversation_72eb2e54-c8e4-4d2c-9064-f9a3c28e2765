'use client';

import { useState } from 'react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50" style={{
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
      borderBottom: '1px solid rgba(255,255,255,0.1)'
    }}>
      <div className="max-w-7xl mx-auto px-8">
        <div className="flex items-center justify-between h-20">

          {/* Logo */}
          <div className="flex items-center space-x-3 cursor-pointer" onClick={() => scrollToSection('hero')}>
            <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-black font-bold text-lg">😊</span>
            </div>
            <span className="text-white font-black text-2xl tracking-wide">BLOOPIX</span>
          </div>

          {/* Center Navigation */}
          <nav className="hidden lg:flex items-center space-x-12">
            <button
              onClick={() => scrollToSection('about')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              Our Story
            </button>
            <button
              onClick={() => scrollToSection('tokenomics')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              Tokenomics
            </button>
            <button
              onClick={() => scrollToSection('roadmap')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              Roadmap
            </button>
            <button
              onClick={() => scrollToSection('faq')}
              className="text-white text-lg font-semibold hover:text-yellow-300 transition-colors duration-200"
            >
              FAQ
            </button>
          </nav>

          {/* Right Side */}
          <div className="hidden lg:flex items-center space-x-6">
            <a
              href="https://t.me/BLOOPIX_OFFICIAL"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white hover:text-yellow-300 transition-colors duration-200"
              title="Join Telegram"
            >
              <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 6.728-.896 6.728-.302 1.507-1.123 1.507-1.123 1.507s-.302 0-.604-.302L12 13.5l-2.945 2.593s-.302.302-.604.302-.302 0-.302-.302c0 0-.604-4.87-.896-6.728-.169-1.858.604-2.16.604-2.16s.906-.302 1.81 0L12 8.16l2.333-.955c.904-.302 1.81 0 1.81 0s.773.302.604 2.16z"/>
              </svg>
            </a>

            <button
              onClick={() => scrollToSection('hero')}
              className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-8 py-3 rounded-full text-lg font-black hover:from-yellow-300 hover:to-orange-400 transition-all duration-200 shadow-xl transform hover:scale-105"
            >
              Buy $BLPX!
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden text-white p-2"
          >
            <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-gray-900/95 backdrop-blur-sm rounded-xl mt-4 py-6 mx-4 shadow-2xl border border-white/10">
            <div className="flex flex-col space-y-4 px-6">
              <button onClick={() => scrollToSection('about')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                Our Story
              </button>
              <button onClick={() => scrollToSection('tokenomics')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                Tokenomics
              </button>
              <button onClick={() => scrollToSection('roadmap')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                Roadmap
              </button>
              <button onClick={() => scrollToSection('faq')} className="text-white text-left py-3 text-lg font-semibold hover:text-yellow-300">
                FAQ
              </button>

              <div className="flex flex-col space-y-4 pt-6 border-t border-white/20">
                <a
                  href="https://t.me/BLOOPIX_OFFICIAL"
                  target="_blank"
                  className="text-white hover:text-yellow-300 flex items-center justify-center space-x-3 py-3"
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 6.728-.896 6.728-.302 1.507-1.123 1.507-1.123 1.507s-.302 0-.604-.302L12 13.5l-2.945 2.593s-.302.302-.604.302-.302 0-.302-.302c0 0-.604-4.87-.896-6.728-.169-1.858.604-2.16.604-2.16s.906-.302 1.81 0L12 8.16l2.333-.955c.904-.302 1.81 0 1.81 0s.773.302.604 2.16z"/>
                  </svg>
                  <span className="text-lg font-semibold">Join Telegram</span>
                </a>

                <button
                  onClick={() => scrollToSection('hero')}
                  className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-8 py-4 rounded-full text-lg font-black shadow-xl"
                >
                  Buy $BLPX!
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
