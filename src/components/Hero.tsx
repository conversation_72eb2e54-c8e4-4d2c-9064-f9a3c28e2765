'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

export default function Hero() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section id="hero" className="relative w-full min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 pt-16 lg:pt-20">
      {/* Professional Background with Particles */}
      <div className="absolute inset-0">
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/90 via-purple-600/90 to-pink-600/90"></div>

        {/* Animated Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.4%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] animate-pulse"></div>
        </div>

        {/* Floating Geometric Shapes */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-white/10 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`,
              }}
            />
          ))}
        </div>
      </div>



      {/* Professional Hero Content - Properly Centered */}
      <div className="relative z-10 w-full flex items-center justify-center min-h-screen">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>

            {/* Professional Logo Section */}
            <div className="mb-16 relative">
              <div className="relative inline-block group">
                {/* Glow Effect */}
                <div className="absolute -inset-8 bg-gradient-to-r from-purple-600/30 via-pink-600/30 to-blue-600/30 rounded-full blur-2xl animate-pulse"></div>

                {/* Main Logo */}
                <div className="relative">
                  <Image
                    src="/bloopix-logo.jpeg"
                    alt="Bloopix Logo"
                    width={280}
                    height={280}
                    className="mx-auto rounded-full border-4 border-white/40 shadow-2xl group-hover:scale-110 transition-transform duration-500 animate-float"
                  />

                  {/* Status Indicators */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-green-400 rounded-full border-3 border-white animate-bounce shadow-lg flex items-center justify-center text-white font-bold text-sm">
                    ✓
                  </div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-yellow-400 rounded-full border-2 border-white animate-pulse shadow-lg"></div>
                </div>
              </div>
            </div>

            {/* Professional Typography */}
            <div className="mb-12">
              <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black text-white mb-6 leading-none tracking-tight">
                <span className="inline-block bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent animate-pulse">
                  Bloopix
                </span>
              </h1>

              <div className="relative inline-block mb-8">
                <span className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-pink-300 to-purple-300 animate-pulse">
                  $BLP
                </span>
                <div className="absolute -inset-4 bg-gradient-to-r from-yellow-400/20 via-pink-400/20 to-purple-400/20 blur-xl rounded-lg"></div>
              </div>

              <div className="w-32 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto rounded-full mb-8"></div>
            </div>

            {/* Professional Description */}
            <p className="text-xl sm:text-2xl md:text-3xl text-white/95 mb-16 max-w-5xl mx-auto leading-relaxed font-medium">
              From the depths of the blockchain ocean comes{' '}
              <span className="text-blue-300 font-bold">Bloopix</span>,
              a bubbly, unpredictable token designed for degens who don&apos;t take themselves too seriously
              but know how to make{' '}
              <span className="text-purple-300 font-bold">waves</span>.
            </p>

            {/* Professional CTA Section */}
            <div className="flex flex-col lg:flex-row gap-8 justify-center items-center mb-16">
              <a
                href="https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative bg-white text-purple-600 px-12 py-6 rounded-full text-xl font-black hover:bg-gray-50 transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-3xl min-w-[320px] overflow-hidden"
              >
                <span className="relative z-10 flex items-center justify-center gap-3">
                  🚀 Buy $BLP on Pump.fun
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>

              <a
                href="https://x.com/bloopix11/status/1947960764603785465?s=46"
                target="_blank"
                rel="noopener noreferrer"
                className="group border-3 border-white text-white px-12 py-6 rounded-full text-xl font-black hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-110 shadow-xl hover:shadow-2xl min-w-[320px]"
              >
                <span className="flex items-center justify-center gap-3">
                  🐦 Follow on X
                </span>
              </a>
            </div>

            {/* Professional Social Proof */}
            <div className="flex flex-wrap justify-center items-center gap-8 mb-16 text-white/80">
              <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="font-semibold">Live on Pump.fun</span>
              </div>
              <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full">
                <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                <span className="font-semibold">Community Driven</span>
              </div>
              <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full">
                <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                <span className="font-semibold">Fair Launch</span>
              </div>
            </div>

            {/* Professional Contract Address */}
            <div className="bg-black/30 backdrop-blur-xl rounded-3xl p-8 max-w-4xl mx-auto border border-white/20 shadow-2xl">
              <p className="text-white/90 text-lg mb-4 font-semibold">Contract Address:</p>
              <div className="flex items-center justify-center gap-4 flex-wrap">
                <code className="bg-white/10 px-6 py-4 rounded-2xl text-base font-mono text-white/95 break-all flex-1 min-w-0 border border-white/10">
                  9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG
                </code>
                <button
                  onClick={() => navigator.clipboard.writeText('9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 px-6 py-4 rounded-2xl transition-all duration-300 text-white font-semibold transform hover:scale-105 shadow-lg"
                  title="Copy to clipboard"
                >
                  📋 Copy
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Bubbles Animation */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-4 h-4 bg-white/20 rounded-full animate-bounce"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center">
          <span className="text-sm mb-2">Scroll to explore</span>
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
}
