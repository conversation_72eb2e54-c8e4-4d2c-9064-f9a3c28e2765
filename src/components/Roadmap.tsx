'use client';

import { useState, useEffect, useRef } from 'react';

export default function Roadmap() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const roadmapPhases = [
    {
      phase: 'Phase 1',
      title: 'Launch 🚀',
      status: 'completed',
      items: [
        'Deploy on Pumpfun ✅',
        'Share memes and graphics on social media ✅',
        'Build a Telegram/X community ✅',
        'Reach early holders and start the bonding curve climb ✅'
      ],
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100'
    },
    {
      phase: 'Phase 2',
      title: 'Community 👥',
      status: 'in-progress',
      items: [
        'Meme contests and giveaways 🎯',
        'Early supporter shout-outs 📢',
        'Stickers, profile pictures, and other shareables 🎨',
        'Community-driven content creation 🎭'
      ],
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100'
    },
    {
      phase: 'Phase 3',
      title: 'Splash Events 🌊',
      status: 'upcoming',
      items: [
        'Possible NFT collaborations 🖼️',
        'IRL or virtual community events 🎉',
        'Partner with other meme projects for cross-promotions 🤝',
        'Major exchange listings (if community demands) 📈'
      ],
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'in-progress':
        return '🔄';
      case 'upcoming':
        return '⏳';
      default:
        return '📋';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      case 'upcoming':
        return 'Upcoming';
      default:
        return 'Planned';
    }
  };

  return (
    <section id="roadmap" ref={sectionRef} className="w-full bg-white">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Professional Section Header */}
          <div className="text-center mb-20">
            <div className="inline-block mb-8">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent text-lg font-bold uppercase tracking-wider">
                Our Journey
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-12 leading-tight">
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent">
                Roadmap
              </span>
            </h2>
            <div className="w-32 h-1.5 bg-gradient-to-r from-purple-600 to-pink-600 mx-auto mb-12 rounded-full"></div>
            <p className="text-xl sm:text-2xl text-gray-700 max-w-5xl mx-auto leading-relaxed font-medium">
              Our journey is simple and transparent. No overblown promises, just organic growth
              driven by community engagement and good vibes.
            </p>
          </div>

          {/* Roadmap Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-green-500 via-blue-500 to-purple-500 rounded-full hidden lg:block"></div>

            {/* Roadmap Items */}
            <div className="space-y-12 lg:space-y-24">
              {roadmapPhases.map((phase, index) => (
                <div
                  key={index}
                  className={`relative ${
                    index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                  } flex flex-col lg:flex items-center`}
                >
                  {/* Timeline Dot */}
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-white border-4 border-gray-300 rounded-full z-10 hidden lg:block">
                    <div className={`w-full h-full bg-gradient-to-r ${phase.color} rounded-full`}></div>
                  </div>

                  {/* Content Card */}
                  <div
                    className={`w-full lg:w-5/12 ${
                      index % 2 === 0 ? 'lg:mr-auto lg:pr-12' : 'lg:ml-auto lg:pl-12'
                    } ${
                      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                    } transition-all duration-1000`}
                    style={{ transitionDelay: `${index * 200}ms` }}
                  >
                    <div className={`bg-gradient-to-br ${phase.bgColor} rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2`}>
                      {/* Phase Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold text-white bg-gradient-to-r ${phase.color} mb-2`}>
                            {phase.phase}
                          </span>
                          <h3 className="text-2xl font-bold text-gray-900">{phase.title}</h3>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl mb-1">{getStatusIcon(phase.status)}</div>
                          <span className={`text-sm font-medium ${
                            phase.status === 'completed' ? 'text-green-600' :
                            phase.status === 'in-progress' ? 'text-blue-600' :
                            'text-purple-600'
                          }`}>
                            {getStatusText(phase.status)}
                          </span>
                        </div>
                      </div>

                      {/* Phase Items */}
                      <ul className="space-y-3">
                        {phase.items.map((item, itemIndex) => (
                          <li
                            key={itemIndex}
                            className="flex items-start space-x-3 text-gray-700"
                          >
                            <span className="text-lg mt-0.5">•</span>
                            <span className="leading-relaxed">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Community Growth Section */}
          <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-4xl font-bold mb-8 text-center">Community & Growth 🌱</h3>
            <p className="text-xl text-center mb-8 text-blue-100 max-w-4xl mx-auto">
              Bloopix thrives on memes, social engagement, and organic excitement. 
              Our growth comes from the community, not from artificial hype.
            </p>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl mb-3">🎨</div>
                <h4 className="font-semibold mb-2">Viral Memes</h4>
                <p className="text-blue-100 text-sm">Creating and sharing viral memes and graphics</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">📱</div>
                <h4 className="font-semibold mb-2">Social Presence</h4>
                <p className="text-blue-100 text-sm">Active on X, Telegram, and other platforms</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">🤝</div>
                <h4 className="font-semibold mb-2">Collaborations</h4>
                <p className="text-blue-100 text-sm">Partnering with other fun tokens and projects</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">🎁</div>
                <h4 className="font-semibold mb-2">Community Rewards</h4>
                <p className="text-blue-100 text-sm">Contests, giveaways, and exclusive content</p>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-16 bg-gray-100 rounded-xl p-6 text-center">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">⚠️ Important Disclaimer</h4>
            <p className="text-gray-600 text-sm leading-relaxed max-w-4xl mx-auto">
              Bloopix is a meme token launched purely for fun and community. It has no intrinsic value, 
              no promised utility, and no guarantee of profit. Anyone buying $BLP does so at their own risk 
              and should only participate if they&apos;re comfortable with the speculative and volatile nature of meme coins.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
