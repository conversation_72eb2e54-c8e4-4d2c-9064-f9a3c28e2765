'use client';

import { useState, useEffect, useRef } from 'react';

export default function Tokenomics() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const tokenomicsData = [
    {
      title: 'Supply',
      value: 'Dynamic',
      description: 'As per Pumpfun\'s dynamic minting system',
      icon: '📊',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Pricing',
      value: 'Bonding Curve',
      description: 'Follows Pumpfun bonding curve - early buyers benefit',
      icon: '📈',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'Distribution',
      value: '100% Fair',
      description: 'No pre-sale, no allocations. Everyone starts equal',
      icon: '⚖️',
      color: 'from-pink-500 to-pink-600'
    },
    {
      title: 'Team Tokens',
      value: '0%',
      description: 'No team tokens reserved',
      icon: '🚫',
      color: 'from-green-500 to-green-600'
    }
  ];

  const highlights = [
    {
      icon: '🏦',
      title: 'No VC Backing',
      description: 'Pure community-driven project with no venture capital involvement'
    },
    {
      icon: '🎯',
      title: 'No Utility Promises',
      description: 'No overblown promises - just fun, community, and good vibes'
    },
    {
      icon: '🌊',
      title: 'Market Waves',
      description: 'Designed to ride the natural waves of market sentiment'
    }
  ];

  return (
    <section id="tokenomics" ref={sectionRef} className="w-full bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 right-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute bottom-40 left-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Professional Section Header */}
          <div className="text-center mb-20">
            <div className="inline-block mb-8">
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent text-lg font-bold uppercase tracking-wider">
                Token Economics
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-12 leading-tight">
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Tokenomics
              </span>
            </h2>
            <div className="w-32 h-1.5 bg-gradient-to-r from-indigo-600 to-purple-600 mx-auto mb-12 rounded-full"></div>
            <p className="text-xl sm:text-2xl text-gray-700 max-w-5xl mx-auto leading-relaxed font-medium">
              Since Bloopix is developed on Pumpfun, it follows the platform&apos;s bonding curve mechanics
              with complete transparency and fairness for all participants.
            </p>
          </div>

          {/* Professional Tokenomics Cards - CENTERED */}
          <div className="w-full flex flex-col items-center justify-center mb-20">
            <div className="w-full max-w-7xl grid sm:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12 mb-8">
              {tokenomicsData.map((item, index) => (
                <div
                  key={index}
                  className={`professional-card p-8 lg:p-10 text-center group hover:scale-105 relative overflow-hidden h-full flex flex-col ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                  }`}
                  style={{ transitionDelay: `${index * 150}ms` }}
                >
                  <div className={`absolute top-0 left-0 right-0 h-2 bg-gradient-to-r ${item.color}`}></div>
                  <div className="text-4xl lg:text-5xl mb-6 group-hover:scale-125 transition-transform duration-300">
                    {item.icon}
                  </div>
                  <h3 className="text-lg lg:text-xl font-black text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                    {item.title}
                  </h3>
                  <div className={`text-2xl lg:text-3xl font-black bg-gradient-to-r ${item.color} bg-clip-text text-transparent mb-4`}>
                    {item.value}
                  </div>
                  <p className="text-gray-600 leading-relaxed text-sm lg:text-base flex-grow">
                    {item.description}
                  </p>
                  <div className={`mt-6 w-full h-1 bg-gradient-to-r ${item.color} rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>
                </div>
              ))}
            </div>
          </div>

          {/* Bonding Curve Explanation */}
          <div className="bg-white rounded-2xl p-8 lg:p-12 shadow-lg mb-20">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-8 text-center">
              🎢 How the Bonding Curve Works
            </h3>
            <div className="grid md:grid-cols-3 gap-8 lg:gap-12 mb-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">1️⃣</span>
                </div>
                <h4 className="text-xl font-semibold mb-3">Early Entry</h4>
                <p className="text-gray-600">Early buyers get the best prices as the curve starts low</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">2️⃣</span>
                </div>
                <h4 className="text-xl font-semibold mb-3">Price Increases</h4>
                <p className="text-gray-600">As more people buy, the price gradually increases along the curve</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">3️⃣</span>
                </div>
                <h4 className="text-xl font-semibold mb-3">Market Discovery</h4>
                <p className="text-gray-600">True market value is discovered through organic demand</p>
              </div>
            </div>
          </div>

          {/* Key Highlights */}
          <div className="grid md:grid-cols-3 gap-8 lg:gap-12 mb-8">
            {highlights.map((highlight, index) => (
              <div
                key={index}
                className={`bg-gradient-to-br from-white to-gray-50 p-6 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${(index + 4) * 100}ms` }}
              >
                <div className="text-3xl mb-4">{highlight.icon}</div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{highlight.title}</h4>
                <p className="text-gray-600 leading-relaxed">{highlight.description}</p>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-3xl font-bold mb-4">Ready to Make Waves? 🌊</h3>
            <p className="text-xl mb-6 text-blue-100">
              Join the Bloopix community and be part of the most fun token on Pumpfun!
            </p>
            <a
              href="https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              🚀 Buy $BLP Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
